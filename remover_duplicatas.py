#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Removedor de Duplicatas Exatas
Remove registros completamente duplicados do arquivo INFO.txt
"""

import os
from collections import OrderedDict
from datetime import datetime

class RemoverDuplicatas:
    def __init__(self, arquivo_entrada: str = "INFO.txt", arquivo_saida: str = "INFO_sem_duplicatas.txt"):
        self.arquivo_entrada = arquivo_entrada
        self.arquivo_saida = arquivo_saida
        self.backup_arquivo = f"INFO_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        self.registros_unicos = OrderedDict()
        self.total_linhas = 0
        self.duplicatas_removidas = 0
        
    def fazer_backup(self):
        """Cria backup do arquivo original"""
        try:
            with open(self.arquivo_entrada, 'r', encoding='utf-8') as origem:
                with open(self.backup_arquivo, 'w', encoding='utf-8') as backup:
                    backup.write(origem.read())
            print(f"✅ Backup criado: {self.backup_arquivo}")
            return True
        except Exception as e:
            print(f"❌ Erro ao criar backup: {e}")
            return False
    
    def processar_arquivo(self):
        """Processa o arquivo removendo duplicatas exatas"""
        print(f"📖 Lendo arquivo: {self.arquivo_entrada}")
        
        try:
            with open(self.arquivo_entrada, 'r', encoding='utf-8') as arquivo:
                for numero_linha, linha in enumerate(arquivo, 1):
                    linha_limpa = linha.strip()
                    
                    if linha_limpa:  # Ignora linhas vazias
                        self.total_linhas += 1
                        
                        # Usa a linha como chave para detectar duplicatas
                        if linha_limpa not in self.registros_unicos:
                            # Primeira ocorrência - mantém
                            self.registros_unicos[linha_limpa] = {
                                'linha_original': numero_linha,
                                'conteudo': linha_limpa
                            }
                        else:
                            # Duplicata encontrada
                            self.duplicatas_removidas += 1
                            print(f"🔍 Duplicata removida (linha {numero_linha}): {linha_limpa[:80]}...")
                            
        except FileNotFoundError:
            print(f"❌ Arquivo não encontrado: {self.arquivo_entrada}")
            return False
        except Exception as e:
            print(f"❌ Erro ao processar arquivo: {e}")
            return False
            
        return True
    
    def salvar_arquivo_limpo(self):
        """Salva arquivo sem duplicatas"""
        print(f"💾 Salvando arquivo limpo: {self.arquivo_saida}")
        
        try:
            with open(self.arquivo_saida, 'w', encoding='utf-8') as arquivo:
                for registro in self.registros_unicos.values():
                    arquivo.write(registro['conteudo'] + '\n')
            
            print(f"✅ Arquivo salvo com sucesso!")
            return True
            
        except Exception as e:
            print(f"❌ Erro ao salvar arquivo: {e}")
            return False
    
    def gerar_relatorio(self):
        """Gera relatório da remoção de duplicatas"""
        registros_unicos = len(self.registros_unicos)
        percentual_duplicatas = (self.duplicatas_removidas / self.total_linhas * 100) if self.total_linhas > 0 else 0
        
        print("\n" + "="*60)
        print("📊 RELATÓRIO DE REMOÇÃO DE DUPLICATAS")
        print("="*60)
        print(f"📄 Arquivo original: {self.arquivo_entrada}")
        print(f"📄 Arquivo limpo: {self.arquivo_saida}")
        print(f"💾 Backup criado: {self.backup_arquivo}")
        print("-"*60)
        print(f"📈 Total de linhas processadas: {self.total_linhas:,}")
        print(f"🗑️  Duplicatas removidas: {self.duplicatas_removidas:,}")
        print(f"✅ Registros únicos mantidos: {registros_unicos:,}")
        print(f"📊 Percentual de duplicatas: {percentual_duplicatas:.2f}%")
        print("-"*60)
        print(f"💾 Redução no tamanho: {self.duplicatas_removidas:,} linhas")
        print("="*60)
        
        # Salva relatório em arquivo
        relatorio_arquivo = f"relatorio_remocao_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        try:
            with open(relatorio_arquivo, 'w', encoding='utf-8') as f:
                f.write("RELATÓRIO DE REMOÇÃO DE DUPLICATAS\n")
                f.write("="*50 + "\n\n")
                f.write(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}\n")
                f.write(f"Arquivo original: {self.arquivo_entrada}\n")
                f.write(f"Arquivo limpo: {self.arquivo_saida}\n")
                f.write(f"Backup: {self.backup_arquivo}\n\n")
                f.write(f"Total de linhas processadas: {self.total_linhas:,}\n")
                f.write(f"Duplicatas removidas: {self.duplicatas_removidas:,}\n")
                f.write(f"Registros únicos mantidos: {registros_unicos:,}\n")
                f.write(f"Percentual de duplicatas: {percentual_duplicatas:.2f}%\n")
                f.write(f"Redução no tamanho: {self.duplicatas_removidas:,} linhas\n")
            
            print(f"📋 Relatório detalhado salvo em: {relatorio_arquivo}")
            
        except Exception as e:
            print(f"⚠️  Erro ao salvar relatório: {e}")
    
    def executar(self):
        """Executa o processo completo de remoção de duplicatas"""
        print("🚀 Iniciando remoção de duplicatas exatas...")
        print("-"*60)
        
        # Verifica se arquivo existe
        if not os.path.exists(self.arquivo_entrada):
            print(f"❌ Arquivo não encontrado: {self.arquivo_entrada}")
            return False
        
        # Cria backup
        if not self.fazer_backup():
            print("❌ Falha ao criar backup. Operação cancelada por segurança.")
            return False
        
        # Processa arquivo
        if not self.processar_arquivo():
            print("❌ Falha ao processar arquivo.")
            return False
        
        # Salva arquivo limpo
        if not self.salvar_arquivo_limpo():
            print("❌ Falha ao salvar arquivo limpo.")
            return False
        
        # Gera relatório
        self.gerar_relatorio()
        
        print("\n🎉 Processo concluído com sucesso!")
        print(f"✅ Arquivo original preservado como backup: {self.backup_arquivo}")
        print(f"✅ Arquivo sem duplicatas: {self.arquivo_saida}")
        
        return True

def main():
    """Função principal"""
    print("🧹 REMOVEDOR DE DUPLICATAS EXATAS")
    print("="*60)
    
    # Pergunta ao usuário sobre substituição do arquivo original
    resposta = input("\n❓ Deseja substituir o arquivo original (INFO.txt)? [s/N]: ").strip().lower()
    
    if resposta in ['s', 'sim', 'y', 'yes']:
        # Substitui o arquivo original
        removedor = RemoverDuplicatas("INFO.txt", "INFO_temp.txt")
        
        if removedor.executar():
            # Move o arquivo temporário para substituir o original
            try:
                os.replace("INFO_temp.txt", "INFO.txt")
                print(f"\n✅ Arquivo original (INFO.txt) foi atualizado!")
                print(f"💾 Backup mantido em: {removedor.backup_arquivo}")
            except Exception as e:
                print(f"❌ Erro ao substituir arquivo original: {e}")
                print(f"📄 Arquivo limpo disponível em: INFO_temp.txt")
    else:
        # Cria novo arquivo
        removedor = RemoverDuplicatas("INFO.txt", "INFO_sem_duplicatas.txt")
        removedor.executar()

if __name__ == "__main__":
    main()
