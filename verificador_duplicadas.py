#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Verificador de Duplicadas para Cartões
Analisa o arquivo INFO.txt e identifica cartões duplicados
"""

import re
from collections import defaultdict
from typing import List, Dict, Tuple, Set

class VerificadorDuplicadas:
    def __init__(self, arquivo_entrada: str = "INFO.txt"):
        self.arquivo_entrada = arquivo_entrada
        self.cartoes = []
        self.duplicadas = defaultdict(list)
        
    def normalizar_cpf(self, cpf: str) -> str:
        """Remove pontos, hífens e espaços do CPF"""
        if not cpf:
            return ""
        return re.sub(r'[.\-\s]', '', cpf.strip())
    
    def normalizar_nome(self, nome: str) -> str:
        """Normaliza o nome removendo acentos e convertendo para maiúsculo"""
        if not nome:
            return ""
        # Remove acentos comuns
        nome = nome.upper().strip()
        acentos = {
            'Á': 'A', 'À': 'A', 'Ã': 'A', 'Â': 'A', 'Ä': 'A',
            'É': 'E', 'È': 'E', 'Ê': 'E', 'Ë': 'E',
            'Í': 'I', 'Ì': 'I', 'Î': 'I', 'Ï': 'I',
            'Ó': 'O', 'Ò': 'O', 'Õ': 'O', 'Ô': 'O', 'Ö': 'O',
            'Ú': 'U', 'Ù': 'U', 'Û': 'U', 'Ü': 'U',
            'Ç': 'C', 'Ñ': 'N'
        }
        for acento, sem_acento in acentos.items():
            nome = nome.replace(acento, sem_acento)
        return nome
    
    def processar_linha(self, linha: str, numero_linha: int) -> Dict:
        """Processa uma linha do arquivo e extrai os dados do cartão"""
        linha = linha.strip()
        if not linha:
            return None
            
        # Split por pipe
        partes = linha.split('|')
        
        if len(partes) < 6:
            print(f"Linha {numero_linha}: Formato inválido - {linha}")
            return None
            
        try:
            numero = partes[0].strip()
            mes = partes[1].strip()
            ano = partes[2].strip()
            cvv = partes[3].strip()
            nome = partes[4].strip()
            cpf = partes[5].strip()
            
            # Normaliza ano (converte 2 dígitos para 4 se necessário)
            if len(ano) == 2:
                ano_int = int(ano)
                if ano_int >= 25:  # Assume que anos >= 25 são 20xx
                    ano = f"20{ano}"
                else:  # Anos < 25 são 20xx
                    ano = f"20{ano}"
            
            return {
                'linha': numero_linha,
                'numero': numero,
                'mes': mes,
                'ano': ano,
                'cvv': cvv,
                'nome': self.normalizar_nome(nome),
                'cpf': self.normalizar_cpf(cpf),
                'linha_original': linha
            }
        except (ValueError, IndexError) as e:
            print(f"Linha {numero_linha}: Erro ao processar - {e}")
            return None
    
    def carregar_dados(self):
        """Carrega e processa todos os dados do arquivo"""
        print(f"Carregando dados de {self.arquivo_entrada}...")
        
        try:
            with open(self.arquivo_entrada, 'r', encoding='utf-8') as arquivo:
                for numero_linha, linha in enumerate(arquivo, 1):
                    cartao = self.processar_linha(linha, numero_linha)
                    if cartao:
                        self.cartoes.append(cartao)
        except FileNotFoundError:
            print(f"Erro: Arquivo {self.arquivo_entrada} não encontrado!")
            return False
        except Exception as e:
            print(f"Erro ao ler arquivo: {e}")
            return False
            
        print(f"Total de cartões carregados: {len(self.cartoes)}")
        return True
    
    def verificar_duplicadas_numero(self):
        """Verifica duplicadas por número do cartão"""
        numeros = defaultdict(list)
        
        for cartao in self.cartoes:
            numeros[cartao['numero']].append(cartao)
        
        duplicadas = {num: cartoes for num, cartoes in numeros.items() if len(cartoes) > 1}
        return duplicadas
    
    def verificar_duplicadas_cpf(self):
        """Verifica duplicadas por CPF"""
        cpfs = defaultdict(list)
        
        for cartao in self.cartoes:
            if cartao['cpf']:  # Ignora CPFs vazios
                cpfs[cartao['cpf']].append(cartao)
        
        duplicadas = {cpf: cartoes for cpf, cartoes in cpfs.items() if len(cartoes) > 1}
        return duplicadas
    
    def verificar_duplicadas_nome(self):
        """Verifica duplicadas por nome"""
        nomes = defaultdict(list)
        
        for cartao in self.cartoes:
            if cartao['nome']:  # Ignora nomes vazios
                nomes[cartao['nome']].append(cartao)
        
        duplicadas = {nome: cartoes for nome, cartoes in nomes.items() if len(cartoes) > 1}
        return duplicadas
    
    def verificar_duplicadas_completas(self):
        """Verifica duplicadas exatas (todos os campos iguais)"""
        chaves = defaultdict(list)
        
        for cartao in self.cartoes:
            chave = f"{cartao['numero']}|{cartao['mes']}|{cartao['ano']}|{cartao['cvv']}|{cartao['nome']}|{cartao['cpf']}"
            chaves[chave].append(cartao)
        
        duplicadas = {chave: cartoes for chave, cartoes in chaves.items() if len(cartoes) > 1}
        return duplicadas
    
    def gerar_relatorio(self):
        """Gera relatório completo de duplicadas"""
        print("\n" + "="*80)
        print("RELATÓRIO DE VERIFICAÇÃO DE DUPLICADAS")
        print("="*80)
        
        # Duplicadas por número
        print("\n1. DUPLICADAS POR NÚMERO DO CARTÃO:")
        print("-" * 50)
        duplicadas_numero = self.verificar_duplicadas_numero()
        
        if duplicadas_numero:
            for numero, cartoes in duplicadas_numero.items():
                print(f"\nNúmero: {numero} ({len(cartoes)} ocorrências)")
                for cartao in cartoes:
                    print(f"  Linha {cartao['linha']}: {cartao['nome']} | CPF: {cartao['cpf']}")
        else:
            print("Nenhuma duplicada encontrada por número do cartão.")
        
        # Duplicadas por CPF
        print("\n\n2. DUPLICADAS POR CPF:")
        print("-" * 50)
        duplicadas_cpf = self.verificar_duplicadas_cpf()
        
        if duplicadas_cpf:
            for cpf, cartoes in duplicadas_cpf.items():
                print(f"\nCPF: {cpf} ({len(cartoes)} ocorrências)")
                for cartao in cartoes:
                    print(f"  Linha {cartao['linha']}: {cartao['numero']} | {cartao['nome']}")
        else:
            print("Nenhuma duplicada encontrada por CPF.")
        
        # Duplicadas por nome
        print("\n\n3. DUPLICADAS POR NOME:")
        print("-" * 50)
        duplicadas_nome = self.verificar_duplicadas_nome()
        
        if duplicadas_nome:
            for nome, cartoes in duplicadas_nome.items():
                if len(cartoes) > 5:  # Mostra apenas nomes com muitas ocorrências
                    print(f"\nNome: {nome} ({len(cartoes)} ocorrências)")
                    for cartao in cartoes[:5]:  # Mostra apenas os primeiros 5
                        print(f"  Linha {cartao['linha']}: {cartao['numero']} | CPF: {cartao['cpf']}")
                    if len(cartoes) > 5:
                        print(f"  ... e mais {len(cartoes) - 5} ocorrências")
        else:
            print("Nenhuma duplicada encontrada por nome.")
        
        # Duplicadas completas
        print("\n\n4. DUPLICADAS COMPLETAS (TODOS OS CAMPOS IGUAIS):")
        print("-" * 50)
        duplicadas_completas = self.verificar_duplicadas_completas()
        
        if duplicadas_completas:
            for chave, cartoes in duplicadas_completas.items():
                print(f"\nRegistro duplicado ({len(cartoes)} ocorrências):")
                print(f"  Dados: {chave}")
                for cartao in cartoes:
                    print(f"  Linha {cartao['linha']}")
        else:
            print("Nenhuma duplicada completa encontrada.")
        
        # Resumo
        print("\n\n5. RESUMO:")
        print("-" * 50)
        print(f"Total de cartões analisados: {len(self.cartoes)}")
        print(f"Números duplicados: {len(duplicadas_numero)}")
        print(f"CPFs duplicados: {len(duplicadas_cpf)}")
        print(f"Nomes duplicados: {len(duplicadas_nome)}")
        print(f"Registros completamente duplicados: {len(duplicadas_completas)}")
        
        return {
            'total_cartoes': len(self.cartoes),
            'duplicadas_numero': duplicadas_numero,
            'duplicadas_cpf': duplicadas_cpf,
            'duplicadas_nome': duplicadas_nome,
            'duplicadas_completas': duplicadas_completas
        }

def main():
    """Função principal"""
    verificador = VerificadorDuplicadas()
    
    if verificador.carregar_dados():
        resultado = verificador.gerar_relatorio()
        
        # Salva resultado em arquivo
        with open('relatorio_duplicadas.txt', 'w', encoding='utf-8') as f:
            f.write("RELATÓRIO DE DUPLICADAS\n")
            f.write("="*50 + "\n\n")
            
            f.write(f"Total de cartões: {resultado['total_cartoes']}\n")
            f.write(f"Números duplicados: {len(resultado['duplicadas_numero'])}\n")
            f.write(f"CPFs duplicados: {len(resultado['duplicadas_cpf'])}\n")
            f.write(f"Nomes duplicados: {len(resultado['duplicadas_nome'])}\n")
            f.write(f"Registros completamente duplicados: {len(resultado['duplicadas_completas'])}\n")
        
        print(f"\nRelatório salvo em 'relatorio_duplicadas.txt'")

if __name__ == "__main__":
    main()
